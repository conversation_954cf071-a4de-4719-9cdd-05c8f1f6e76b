"""
Compliance API endpoints with AI-powered analysis
"""
from typing import Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session

from app.api import deps
from app.db.database import get_db
from app.models.user import User
from app.services.compliance_ai import compliance_ai_service
from app.crud.crud_compliance import (
    crud_compliance_category,
    crud_compliance_requirement,
    crud_compliance_assessment,
    crud_regulatory_update,
    crud_compliance_dashboard
)
from app.schemas.compliance import (
    ComplianceCategory,
    ComplianceCategoryCreate,
    ComplianceCategoryUpdate,
    ComplianceRequirement,
    ComplianceRequirementCreate,
    ComplianceRequirementUpdate,
    ComplianceAssessment,
    ComplianceAssessmentCreate,
    ComplianceAssessmentUpdate,
    RegulatoryUpdate,
    RegulatoryUpdateCreate,
    RegulatoryUpdateUpdate,
    ComplianceDashboardSummary
)
from app.services.export_service import export_service

router = APIRouter()


# Dashboard endpoints
@router.get("/dashboard", response_model=dict)
def get_compliance_dashboard(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """Get compliance dashboard summary"""
    summary = crud_compliance_dashboard.get_dashboard_summary(
        db, organization_id=current_user.organization_id
    )
    return summary


# Compliance Categories endpoints
@router.get("/categories", response_model=List[ComplianceCategory])
def get_compliance_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_active: Optional[bool] = Query(None)
):
    """Get compliance categories"""
    categories = crud_compliance_category.get_by_organization(
        db,
        organization_id=current_user.organization_id,
        skip=skip,
        limit=limit,
        is_active=is_active
    )
    return categories


@router.post("/categories", response_model=ComplianceCategory)
def create_compliance_category(
    *,
    db: Session = Depends(get_db),
    category_in: ComplianceCategoryCreate,
    current_user: User = Depends(deps.check_permission("compliance:create"))
):
    """Create new compliance category"""
    # Check if category code already exists
    existing = crud_compliance_category.get_by_code(
        db, code=category_in.code, organization_id=current_user.organization_id
    )
    if existing:
        raise HTTPException(status_code=400, detail="Category code already exists")
    
    category_data = category_in.dict()
    category_data["organization_id"] = current_user.organization_id
    
    category = crud_compliance_category.create(db=db, obj_in=category_data)
    return category


@router.get("/categories/{category_id}", response_model=ComplianceCategory)
def get_compliance_category(
    category_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """Get specific compliance category"""
    category = crud_compliance_category.get(db, id=category_id)
    if not category:
        raise HTTPException(status_code=404, detail="Category not found")
    
    if category.organization_id != current_user.organization_id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return category


@router.put("/categories/{category_id}", response_model=ComplianceCategory)
def update_compliance_category(
    category_id: UUID,
    category_update: ComplianceCategoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permission("compliance:update"))
):
    """Update compliance category"""
    category = crud_compliance_category.get(db, id=category_id)
    if not category:
        raise HTTPException(status_code=404, detail="Category not found")
    
    if category.organization_id != current_user.organization_id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    category = crud_compliance_category.update(db=db, db_obj=category, obj_in=category_update)
    return category


# Compliance Requirements endpoints
@router.get("/requirements", response_model=List[ComplianceRequirement])
def get_compliance_requirements(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    category_id: Optional[UUID] = Query(None)
):
    """Get compliance requirements"""
    requirements = crud_compliance_requirement.get_by_organization(
        db,
        organization_id=current_user.organization_id,
        skip=skip,
        limit=limit,
        status=status,
        category_id=category_id
    )
    return requirements


@router.post("/requirements", response_model=ComplianceRequirement)
def create_compliance_requirement(
    *,
    db: Session = Depends(get_db),
    requirement_in: ComplianceRequirementCreate,
    current_user: User = Depends(deps.check_permission("compliance:create"))
):
    """Create new compliance requirement"""
    requirement_data = requirement_in.dict()
    requirement_data["organization_id"] = current_user.organization_id
    
    requirement = crud_compliance_requirement.create(db=db, obj_in=requirement_data)
    return requirement


@router.get("/requirements/overdue", response_model=List[ComplianceRequirement])
def get_overdue_requirements(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """Get overdue compliance requirements"""
    requirements = crud_compliance_requirement.get_overdue(
        db,
        organization_id=current_user.organization_id,
        skip=skip,
        limit=limit
    )
    return requirements


@router.get("/requirements/upcoming-reviews", response_model=List[ComplianceRequirement])
def get_upcoming_reviews(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    days_ahead: int = Query(30, ge=1, le=365),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """Get requirements with upcoming reviews"""
    requirements = crud_compliance_requirement.get_upcoming_reviews(
        db,
        organization_id=current_user.organization_id,
        days_ahead=days_ahead,
        skip=skip,
        limit=limit
    )
    return requirements


@router.get("/requirements/{requirement_id}", response_model=ComplianceRequirement)
def get_compliance_requirement(
    requirement_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """Get specific compliance requirement"""
    requirement = crud_compliance_requirement.get(db, id=requirement_id)
    if not requirement:
        raise HTTPException(status_code=404, detail="Requirement not found")
    
    if requirement.organization_id != current_user.organization_id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return requirement


@router.put("/requirements/{requirement_id}", response_model=ComplianceRequirement)
def update_compliance_requirement(
    requirement_id: UUID,
    requirement_update: ComplianceRequirementUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permission("compliance:update"))
):
    """Update compliance requirement"""
    requirement = crud_compliance_requirement.get(db, id=requirement_id)
    if not requirement:
        raise HTTPException(status_code=404, detail="Requirement not found")
    
    if requirement.organization_id != current_user.organization_id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    requirement = crud_compliance_requirement.update(db=db, db_obj=requirement, obj_in=requirement_update)
    return requirement


# Compliance Assessments endpoints
@router.get("/assessments", response_model=List[ComplianceAssessment])
def get_compliance_assessments(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    assessment_type: Optional[str] = Query(None)
):
    """Get compliance assessments"""
    assessments = crud_compliance_assessment.get_by_organization(
        db,
        organization_id=current_user.organization_id,
        skip=skip,
        limit=limit,
        assessment_type=assessment_type
    )
    return assessments


@router.post("/assessments", response_model=ComplianceAssessment)
def create_compliance_assessment(
    *,
    db: Session = Depends(get_db),
    assessment_in: ComplianceAssessmentCreate,
    current_user: User = Depends(deps.check_permission("compliance:assess"))
):
    """Create new compliance assessment"""
    assessment_data = assessment_in.dict()
    assessment_data["organization_id"] = current_user.organization_id
    assessment_data["assessed_by_id"] = current_user.id
    
    assessment = crud_compliance_assessment.create(db=db, obj_in=assessment_data)
    return assessment


# Regulatory Updates endpoints
@router.get("/regulatory-updates", response_model=List[RegulatoryUpdate])
def get_regulatory_updates(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_reviewed: Optional[bool] = Query(None),
    impact_level: Optional[str] = Query(None)
):
    """Get regulatory updates"""
    updates = crud_regulatory_update.get_by_organization(
        db,
        organization_id=current_user.organization_id,
        skip=skip,
        limit=limit,
        is_reviewed=is_reviewed,
        impact_level=impact_level
    )
    return updates


@router.post("/regulatory-updates", response_model=RegulatoryUpdate)
def create_regulatory_update(
    *,
    db: Session = Depends(get_db),
    update_in: RegulatoryUpdateCreate,
    current_user: User = Depends(deps.check_permission("compliance:create"))
):
    """Create new regulatory update"""
    update_data = update_in.dict()
    update_data["organization_id"] = current_user.organization_id
    
    update = crud_regulatory_update.create(db=db, obj_in=update_data)
    return update


@router.get("/regulatory-updates/unreviewed", response_model=List[RegulatoryUpdate])
def get_unreviewed_updates(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """Get unreviewed regulatory updates"""
    updates = crud_regulatory_update.get_unreviewed(
        db,
        organization_id=current_user.organization_id,
        skip=skip,
        limit=limit
    )
    return updates


# AI-Powered Compliance Analysis Endpoints

@router.post("/ai/analyze", response_model=dict)
async def analyze_organization_compliance(
    analysis_type: str = Query(default="comprehensive", description="Type of analysis to perform"),
    regulatory_focus: Optional[List[str]] = Query(default=None, description="Specific regulatory domains to focus on"),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Perform AI-powered compliance analysis for the organization

    Analysis types:
    - comprehensive: Full compliance analysis
    - violation_detection: Focus on detecting violations
    - regulatory_monitoring: Monitor regulatory changes
    - pattern_analysis: Analyze compliance patterns
    - trend_analysis: Analyze compliance trends
    - risk_assessment: Assess compliance risks
    """
    try:
        result = await compliance_ai_service.analyze_organization_compliance(
            db=db,
            organization_id=current_user.organization_id,
            analysis_type=analysis_type,
            regulatory_focus=regulatory_focus
        )

        return {
            "status": "success",
            "analysis_type": analysis_type,
            "organization_id": str(current_user.organization_id),
            "result": result.dict()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Compliance analysis failed: {str(e)}")


@router.post("/ai/analyze/requirement/{requirement_id}", response_model=dict)
async def analyze_compliance_requirement(
    requirement_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Perform AI-powered analysis of a specific compliance requirement
    """
    try:
        result = await compliance_ai_service.analyze_compliance_requirement(
            db=db,
            requirement_id=requirement_id,
            organization_id=current_user.organization_id
        )

        return {
            "status": "success",
            "requirement_id": str(requirement_id),
            "organization_id": str(current_user.organization_id),
            "result": result.dict()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Requirement analysis failed: {str(e)}")


@router.post("/ai/monitoring/setup", response_model=dict)
async def setup_regulatory_monitoring(
    monitoring_scope: Optional[List[str]] = Query(default=None, description="Regulatory domains to monitor"),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Set up AI-powered regulatory change monitoring

    Monitoring scope options: sec, finra, aml, sox, gdpr, basel, dodd_frank, mifid
    """
    try:
        result = await compliance_ai_service.monitor_regulatory_changes(
            db=db,
            organization_id=current_user.organization_id,
            monitoring_scope=monitoring_scope
        )

        return {
            "status": "success",
            "organization_id": str(current_user.organization_id),
            "monitoring_setup": result.dict()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Monitoring setup failed: {str(e)}")


@router.post("/ai/analyze/regulatory-update", response_model=dict)
async def analyze_regulatory_update(
    update_content: str,
    regulation_type: str,
    organization_context: Optional[dict] = None,
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Analyze the impact of a specific regulatory update using AI

    Regulation types: sec, finra, aml, sox, gdpr, basel, dodd_frank, mifid, general
    """
    try:
        result = await compliance_ai_service.analyze_regulatory_update(
            update_content=update_content,
            regulation_type=regulation_type,
            organization_context=organization_context or {}
        )

        return {
            "status": "success",
            "regulation_type": regulation_type,
            "organization_id": str(current_user.organization_id),
            "analysis": result.dict()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Regulatory update analysis failed: {str(e)}")


@router.get("/ai/status", response_model=dict)
def get_compliance_ai_status(
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get current status and capabilities of the Compliance AI service
    """
    try:
        status = compliance_ai_service.get_service_status()

        return {
            "status": "success",
            "service_status": status,
            "organization_id": str(current_user.organization_id)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get AI status: {str(e)}")


@router.get("/ai/insights/summary", response_model=dict)
async def get_compliance_ai_insights_summary(
    days: int = Query(default=30, description="Number of days to include in summary"),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get a summary of recent AI compliance insights and trends
    """
    try:
        # Perform quick analysis for insights summary
        result = await compliance_ai_service.analyze_organization_compliance(
            db=db,
            organization_id=current_user.organization_id,
            analysis_type="trend_analysis",
            regulatory_focus=None
        )

        # Extract key insights for summary
        insights_summary = {
            "compliance_score": result.ai_insights.compliance_score,
            "risk_level": result.ai_insights.risk_level,
            "violations_count": len(result.ai_insights.violations_detected),
            "regulatory_changes_count": len(result.ai_insights.regulatory_changes),
            "active_alerts_count": len(result.ai_insights.monitoring_alerts),
            "recommendations_count": len(result.ai_insights.recommendations),
            "confidence_level": result.ai_insights.confidence_level,
            "analysis_date": result.analysis_date,
            "model_version": result.model_version
        }

        return {
            "status": "success",
            "organization_id": str(current_user.organization_id),
            "time_period_days": days,
            "insights_summary": insights_summary
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get insights summary: {str(e)}")


@router.post("/reports/export/pdf")
async def export_compliance_report_pdf(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    audit_scope: dict = None,
    report_format: str = "detailed",
    compliance_areas: List[str] = None
) -> Any:
    """
    Export compliance report to PDF format
    """
    try:
        # Get compliance data for the organization
        organization_id = current_user.organization_id

        # Get compliance categories and assessments
        categories = crud_compliance_category.get_by_organization(db=db, organization_id=organization_id)
        assessments = crud_compliance_assessment.get_by_organization(db=db, organization_id=organization_id)

        # Calculate compliance metrics
        total_assessments = len(assessments)
        passed_assessments = len([a for a in assessments if a.status == "compliant"])
        failed_assessments = len([a for a in assessments if a.status == "non_compliant"])
        warning_assessments = len([a for a in assessments if a.status == "warning"])

        compliance_score = (passed_assessments / total_assessments * 100) if total_assessments > 0 else 0

        # Prepare compliance data for export
        compliance_data = {
            "audit_period": audit_scope.get("audit_period", "Last 30 days") if audit_scope else "Last 30 days",
            "compliance_areas": compliance_areas or ["All"],
            "report_type": report_format,
            "summary": {
                "total_checks": total_assessments,
                "passed": passed_assessments,
                "failed": failed_assessments,
                "warnings": warning_assessments,
                "compliance_score": compliance_score
            },
            "findings": []
        }

        # Add detailed findings
        for assessment in assessments:
            finding = {
                "category": assessment.category.name if assessment.category else "Unknown",
                "status": assessment.status,
                "description": assessment.assessment_details.get("description", "") if assessment.assessment_details else "",
                "risk_level": assessment.risk_level or "Medium"
            }
            compliance_data["findings"].append(finding)

        # Get organization name
        organization_name = current_user.organization.name if current_user.organization else "DealVerse Organization"

        # Export to PDF
        pdf_data = await export_service.export_compliance_report_to_pdf(
            compliance_data=compliance_data,
            organization_name=organization_name
        )

        from fastapi.responses import Response

        return Response(
            content=pdf_data,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=compliance_report_{organization_name.replace(' ', '_')}.pdf"
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to export compliance report: {str(e)}"
        )
